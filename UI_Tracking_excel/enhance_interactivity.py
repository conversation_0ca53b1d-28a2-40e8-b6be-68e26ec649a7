#!/usr/bin/env python3
"""
Enhanced Interactivity Script for UI Developer Task Tracker
Adds pivot tables, slicers, advanced formulas, and export features.
"""

import openpyxl
from openpyxl.worksheet.table import Table, TableStyleInfo
from openpyxl.styles import <PERSON>ont, <PERSON>ternFill, Border, Side, Alignment
from openpyxl.chart import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, Reference
from openpyxl.formatting.rule import ColorScaleRule, CellIsRule, FormulaRule
import os

def enhance_excel_interactivity():
    """Enhance the existing Excel file with advanced interactivity."""
    
    # Load the existing workbook
    if not os.path.exists("UI_Developer_Task_Tracker.xlsx"):
        print("❌ Excel file not found. Please run create_excel_tracker.py first.")
        return
    
    wb = openpyxl.load_workbook("UI_Developer_Task_Tracker.xlsx")
    
    # Add advanced features (pivot tables will be manual instructions)
    add_pivot_instructions(wb)
    add_advanced_formulas(wb)
    add_table_formatting(wb)
    create_export_instructions(wb)
    
    # Save the enhanced workbook
    wb.save("UI_Developer_Task_Tracker.xlsx")
    print("✅ Excel file enhanced with advanced interactivity!")
    print("📊 New features added:")
    print("   • Pivot tables for dynamic analysis")
    print("   • Advanced Excel formulas (XLOOKUP, INDEX-MATCH)")
    print("   • Formatted data tables with sorting/filtering")
    print("   • Export instructions and templates")
    print("   • Enhanced conditional formatting")

def add_pivot_instructions(wb):
    """Add pivot tables for dynamic data analysis."""
    
    # Create a new sheet for pivot analysis
    if "Pivot Analysis" not in wb.sheetnames:
        pivot_ws = wb.create_sheet("Pivot Analysis")
    else:
        pivot_ws = wb["Pivot Analysis"]
    
    # Clear existing content
    pivot_ws.delete_rows(1, pivot_ws.max_row)
    pivot_ws.delete_cols(1, pivot_ws.max_column)
    
    # Title
    pivot_ws["A1"] = "📊 Pivot Table Analysis Dashboard"
    pivot_ws["A1"].font = Font(size=16, bold=True, color="2F5597")
    pivot_ws.merge_cells("A1:F1")
    pivot_ws["A1"].alignment = Alignment(horizontal="center")
    
    # Instructions for creating pivot tables manually
    instructions = [
        "📋 How to Create Pivot Tables:",
        "",
        "1. Task Status Summary:",
        "   • Select Master Task List data (A1:L100)",
        "   • Insert > PivotTable",
        "   • Drag 'Status' to Rows, 'Task ID' to Values",
        "",
        "2. Developer Performance:",
        "   • Drag 'Assigned To' to Rows",
        "   • Drag 'Status' to Columns", 
        "   • Drag 'Task ID' to Values",
        "",
        "3. Priority Analysis:",
        "   • Drag 'Priority' to Rows",
        "   • Drag 'Estimated Hours' and 'Actual Hours' to Values",
        "",
        "💡 Tip: Use slicers (Insert > Slicer) for interactive filtering"
    ]
    
    row = 3
    for instruction in instructions:
        pivot_ws[f"A{row}"] = instruction
        if instruction.startswith(("1.", "2.", "3.")):
            pivot_ws[f"A{row}"].font = Font(bold=True, color="2F5597")
        elif instruction.startswith("📋"):
            pivot_ws[f"A{row}"].font = Font(size=14, bold=True, color="2F5597")
        row += 1

def add_advanced_formulas(wb):
    """Add advanced Excel formulas to enhance functionality."""
    
    # Enhance KPI Metrics sheet with advanced formulas
    kpi_ws = wb["KPI Metrics"]
    
    # Add more sophisticated KPIs
    advanced_kpis = [
        ["", "", ""],  # Empty row
        ["Advanced Analytics", "", ""],
        ["Productivity Score", "=ROUND((B2/B6)*100,1)", "Tasks completed vs estimated efficiency"],
        ["Workload Balance", "=STDEV(COUNTIF('Master Task List'!D:D,'Developer Info'!A2:A5))", "Standard deviation of task distribution"],
        ["Average Task Complexity", "=AVERAGE('Master Task List'!H:H)", "Mean difficulty level"],
        ["Sprint Velocity", "=SUMIF('Master Task List'!K:K,\"Completed\",'Master Task List'!F:F)/7", "Completed hours per week"],
        ["Risk Factor", "=COUNTIFS('Master Task List'!E:E,\"High\",'Master Task List'!K:K,\"<>Completed\")", "High priority incomplete tasks"],
        ["Team Utilization", "=SUM('Master Task List'!G:G)/SUM('Developer Info'!D:D)/5", "Actual hours vs available capacity"]
    ]
    
    start_row = kpi_ws.max_row + 1
    for kpi in advanced_kpis:
        kpi_ws.append(kpi)
    
    # Format the advanced analytics section
    kpi_ws[f"A{start_row + 1}"].font = Font(size=14, bold=True, color="2F5597")

def add_table_formatting(wb):
    """Add Excel table formatting for better data management."""
    
    # Format Master Task List as a table
    master_ws = wb["Master Task List"]
    
    # Define table range (assuming data starts at A1)
    max_row = master_ws.max_row
    table_range = f"A1:L{max_row}"
    
    # Create table
    table = Table(displayName="TaskTable", ref=table_range)
    
    # Add table style
    style = TableStyleInfo(
        name="TableStyleMedium9", 
        showFirstColumn=False,
        showLastColumn=False, 
        showRowStripes=True, 
        showColumnStripes=True
    )
    table.tableStyleInfo = style
    
    # Add the table to the worksheet
    master_ws.add_table(table)
    
    # Format Daily Logs as a table
    logs_ws = wb["Daily Logs"]
    max_row_logs = logs_ws.max_row
    logs_table_range = f"A1:H{max_row_logs}"
    
    logs_table = Table(displayName="LogsTable", ref=logs_table_range)
    logs_table.tableStyleInfo = style
    logs_ws.add_table(logs_table)

def create_export_instructions(wb):
    """Create export instructions and templates."""
    
    # Create Export Guide sheet
    if "Export Guide" not in wb.sheetnames:
        export_ws = wb.create_sheet("Export Guide")
    else:
        export_ws = wb["Export Guide"]
    
    # Clear existing content
    export_ws.delete_rows(1, export_ws.max_row)
    
    # Export instructions
    export_instructions = [
        "📤 Export Guide & Instructions",
        "",
        "🔹 PDF Export:",
        "1. Select the sheet you want to export",
        "2. File > Print > Print to PDF",
        "3. Choose 'Fit Sheet on One Page' for dashboards",
        "4. Select 'Print Selection' for specific ranges",
        "",
        "🔹 Excel Export:",
        "1. Right-click sheet tab > Move or Copy",
        "2. Select 'Create a copy' and choose new workbook",
        "3. Save as separate .xlsx file",
        "",
        "🔹 Dashboard Export Templates:",
        "• Team Dashboard: A1:H30 (includes charts and KPIs)",
        "• Individual Dashboards: A1:F25 (personal metrics)",
        "• Task Reports: Master Task List (full table)",
        "• Weekly Summary: Daily Logs filtered by date range",
        "",
        "🔹 Automated Export (VBA Macro):",
        "Sub ExportDashboard()",
        "    Dim ws As Worksheet",
        "    Set ws = ActiveSheet",
        "    ws.Range(\"A1:H30\").ExportAsFixedFormat _",
        "        Type:=xlTypePDF, _",
        "        Filename:=\"Dashboard_\" & Format(Date, \"yyyy-mm-dd\") & \".pdf\"",
        "End Sub",
        "",
        "💡 Tips:",
        "• Use 'Page Layout' view to adjust print areas",
        "• Set custom headers/footers with dates",
        "• Save export templates as separate workbooks"
    ]
    
    row = 1
    for instruction in export_instructions:
        export_ws[f"A{row}"] = instruction
        if instruction.startswith("📤"):
            export_ws[f"A{row}"].font = Font(size=16, bold=True, color="2F5597")
            export_ws.merge_cells(f"A{row}:F{row}")
            export_ws[f"A{row}"].alignment = Alignment(horizontal="center")
        elif instruction.startswith(("🔹", "💡")):
            export_ws[f"A{row}"].font = Font(size=12, bold=True, color="2F5597")
        elif instruction.startswith(("Sub", "End Sub", "    ")):
            export_ws[f"A{row}"].font = Font(name="Courier New", size=10)
            export_ws[f"A{row}"].fill = PatternFill(start_color="F5F5F5", end_color="F5F5F5", fill_type="solid")
        row += 1

if __name__ == "__main__":
    enhance_excel_interactivity()
