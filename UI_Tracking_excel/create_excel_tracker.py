#!/usr/bin/env python3
"""
UI Developer Task Tracker Excel Generator
Creates a comprehensive Excel workbook with all required sheets and basic structure.
"""

import pandas as pd
from datetime import datetime, timedelta
import openpyxl
from openpyxl.styles import <PERSON>ont, PatternFill, Border, Side, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.chart import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Reference
from openpyxl.chart.data_source import NumData, NumVal
from openpyxl.formatting.rule import ColorScaleRule, CellIsRule, FormulaRule
from openpyxl.styles.differential import DifferentialStyle
from openpyxl.workbook.defined_name import DefinedName
from openpyxl.worksheet.datavalidation import DataValidation
import random

def create_ui_task_tracker():
    """Create the comprehensive UI Developer Task Tracker Excel file."""
    
    # Create a new workbook
    wb = openpyxl.Workbook()
    
    # Remove default sheet
    wb.remove(wb.active)
    
    # Define developers
    developers = [
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>", 
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>"
    ]
    
    # Define sheet names in order
    sheet_names = [
        "Dashboard",
        "Master Task List", 
        "Daily Logs",
        "Developer Info",
        "Task Assignment Tracker",
        "KPI Metrics",
        "Srishti Dashboard",
        "Japneet Dashboard", 
        "Chittu Dashboard",
        "Shivani Dashboard"
    ]
    
    # Create all sheets
    for sheet_name in sheet_names:
        wb.create_sheet(title=sheet_name)
    
    # Set Dashboard as active sheet
    wb.active = wb["Dashboard"]
    
    # Create Developer Info sheet first (as it's referenced by others)
    create_developer_info_sheet(wb["Developer Info"], developers)
    
    # Create Master Task List sheet
    create_master_task_list_sheet(wb["Master Task List"], developers)
    
    # Create Daily Logs sheet
    create_daily_logs_sheet(wb["Daily Logs"], developers)
    
    # Create KPI Metrics sheet
    create_kpi_metrics_sheet(wb["KPI Metrics"])
    
    # Create Task Assignment Tracker sheet
    create_task_assignment_tracker_sheet(wb["Task Assignment Tracker"])
    
    # Create Main Dashboard
    create_main_dashboard_sheet(wb["Dashboard"])
    
    # Create Individual Developer Dashboards
    for dev in developers:
        sheet_name = f"{dev.split()[0]} Dashboard"
        create_enhanced_individual_dashboard(wb[sheet_name], dev)

    # Add navigation to all sheets
    add_navigation_buttons(wb)

    # Save the workbook
    wb.save("UI_Developer_Task_Tracker.xlsx")
    print("✅ UI Developer Task Tracker Excel file created successfully!")
    print("📊 Features included:")
    print("   • 10 interconnected sheets with live data")
    print("   • Data validation and conditional formatting")
    print("   • Interactive charts and KPI dashboards")
    print("   • Individual developer performance tracking")
    print("   • Navigation buttons between sheets")
    print("   • Sample data for immediate testing")
    return wb

def create_developer_info_sheet(ws, developers):
    """Create the Developer Info sheet with profile data."""
    
    # Headers
    headers = ["Developer Name", "Skills", "Join Date", "Daily Working Hours", "Availability Status"]
    ws.append(headers)
    
    # Sample data for developers
    skills_list = [
        "React, JavaScript, CSS, HTML, UI/UX Design",
        "Vue.js, TypeScript, SASS, Responsive Design", 
        "Angular, JavaScript, Bootstrap, Material UI",
        "React Native, Flutter, Mobile UI, Figma"
    ]
    
    join_dates = ["2023-01-15", "2023-03-20", "2022-11-10", "2023-05-05"]
    working_hours = [8, 8, 7.5, 8]
    availability = ["Available", "Available", "Busy", "Available"]
    
    for i, dev in enumerate(developers):
        ws.append([dev, skills_list[i], join_dates[i], working_hours[i], availability[i]])
    
    # Apply formatting
    apply_header_formatting(ws, len(headers))

def create_master_task_list_sheet(ws, developers):
    """Create the Master Task List sheet with task tracking columns."""

    # Headers
    headers = [
        "Task ID", "Task Title", "Description", "Assigned To", "Priority",
        "Estimated Hours", "Actual Hours", "Difficulty Level", "Start Date",
        "End Date", "Status", "% Completion"
    ]
    ws.append(headers)

    # Sample tasks with more realistic data
    sample_tasks = [
        ["T001", "Login Page Redesign", "Redesign the login page with new branding guidelines", "Srishti Singh", "High", 16, 14, 3, "2024-01-15", "2024-01-20", "Completed", 100],
        ["T002", "Dashboard Components", "Create reusable dashboard components library", "Japneet Doulani", "Medium", 24, 20, 4, "2024-01-18", "2024-01-25", "In Progress", 75],
        ["T003", "Mobile Responsive Fix", "Fix mobile responsiveness issues across all pages", "Chittu Dhanraj Chavan", "High", 12, 8, 2, "2024-01-20", "2024-01-23", "In Progress", 60],
        ["T004", "User Profile Module", "Develop comprehensive user profile management", "Shivani Singh", "Medium", 20, 0, 3, "2024-01-22", "2024-01-30", "Not Started", 0],
        ["T005", "Navigation Menu", "Implement new responsive navigation structure", "Srishti Singh", "Low", 8, 6, 2, "2024-01-16", "2024-01-19", "Completed", 100],
        ["T006", "Search Functionality", "Add advanced search with filters", "Japneet Doulani", "High", 18, 5, 4, "2024-01-25", "2024-02-02", "In Progress", 25],
        ["T007", "Data Tables", "Create sortable and filterable data tables", "Chittu Dhanraj Chavan", "Medium", 14, 0, 3, "2024-01-28", "2024-02-05", "Not Started", 0],
        ["T008", "Form Validation", "Implement client-side form validation", "Shivani Singh", "High", 10, 8, 2, "2024-01-24", "2024-01-26", "In Progress", 80],
        ["T009", "Theme Customization", "Add dark/light theme toggle", "Srishti Singh", "Low", 12, 0, 2, "2024-02-01", "2024-02-08", "Not Started", 0],
        ["T010", "Performance Optimization", "Optimize page load times and animations", "Japneet Doulani", "Medium", 16, 2, 4, "2024-01-30", "2024-02-06", "In Progress", 15]
    ]

    for task in sample_tasks:
        ws.append(task)

    # Apply formatting
    apply_header_formatting(ws, len(headers))

    # Add data validation for dropdowns
    add_master_task_validation(ws, developers)

    # Add conditional formatting
    add_master_task_conditional_formatting(ws)

def create_daily_logs_sheet(ws, developers):
    """Create the Daily Logs sheet for time tracking."""
    
    # Headers
    headers = ["Developer", "Date", "Task ID", "Start Time", "End Time", "Total Hours", "Task Status", "Notes"]
    ws.append(headers)
    
    # Sample daily logs
    sample_logs = [
        ["Srishti Singh", "2024-01-15", "T001", "09:00", "17:00", 8, "In Progress", "Working on login form layout"],
        ["Japneet Doulani", "2024-01-18", "T002", "09:30", "17:30", 8, "In Progress", "Created base dashboard components"],
        ["Chittu Dhanraj Chavan", "2024-01-20", "T003", "10:00", "16:00", 6, "In Progress", "Fixed tablet view issues"],
        ["Shivani Singh", "2024-01-22", "T004", "09:00", "12:00", 3, "Not Started", "Planning and research phase"]
    ]
    
    for log in sample_logs:
        ws.append(log)

    # Apply formatting
    apply_header_formatting(ws, len(headers))

    # Add data validation for developers
    dev_validation = DataValidation(type="list", formula1=f'"{",".join(developers)}"')
    dev_validation.error = "Please select a valid developer"
    dev_validation.errorTitle = "Invalid Developer"
    ws.add_data_validation(dev_validation)
    dev_validation.add("A2:A1000")  # Developer column

def create_kpi_metrics_sheet(ws):
    """Create the KPI Metrics sheet with calculated metrics."""
    
    # KPI Headers and Formulas
    kpis = [
        ["Metric", "Value", "Formula/Description"],
        ["Total Tasks", "=COUNTA('Master Task List'!A:A)-1", "Count of all tasks"],
        ["Completed Tasks", "=COUNTIF('Master Task List'!K:K,\"Completed\")", "Count of completed tasks"],
        ["In Progress Tasks", "=COUNTIF('Master Task List'!K:K,\"In Progress\")", "Count of in-progress tasks"],
        ["Not Started Tasks", "=COUNTIF('Master Task List'!K:K,\"Not Started\")", "Count of not started tasks"],
        ["Total Hours Estimated", "=SUM('Master Task List'!F:F)", "Sum of estimated hours"],
        ["Total Hours Actual", "=SUM('Master Task List'!G:G)", "Sum of actual hours"],
        ["Average Completion %", "=AVERAGE('Master Task List'!L:L)", "Average completion percentage"],
        ["High Priority Tasks", "=COUNTIF('Master Task List'!E:E,\"High\")", "Count of high priority tasks"],
        ["Overdue Tasks", "=COUNTIFS('Master Task List'!J:J,\"<\"&TODAY(),'Master Task List'!K:K,\"<>Completed\")", "Tasks past due date"]
    ]
    
    for kpi in kpis:
        ws.append(kpi)
    
    # Apply formatting
    apply_header_formatting(ws, 3)

def create_task_assignment_tracker_sheet(ws):
    """Create the Task Assignment Tracker sheet."""
    
    # Headers
    headers = ["Task ID", "Task Title", "Assigned To", "Priority", "Estimated Hours", "Skills Required", "Status", "Assignment Date"]
    ws.append(headers)
    
    # This will reference Master Task List data
    ws.append(["=INDIRECT(\"'Master Task List'!A2\")", "=INDIRECT(\"'Master Task List'!B2\")", 
               "=INDIRECT(\"'Master Task List'!D2\")", "=INDIRECT(\"'Master Task List'!E2\")",
               "=INDIRECT(\"'Master Task List'!F2\")", "Skills from Dev Info", 
               "=INDIRECT(\"'Master Task List'!K2\")", "Assignment Date"])
    
    # Apply formatting
    apply_header_formatting(ws, len(headers))

def create_main_dashboard_sheet(ws):
    """Create the main team dashboard with charts and KPIs."""

    # Dashboard title
    ws["A1"] = "UI Developer Task Tracker - Team Dashboard"
    ws["A1"].font = Font(size=18, bold=True, color="2F5597")
    ws.merge_cells("A1:H1")
    ws["A1"].alignment = Alignment(horizontal="center")

    # Key metrics section
    ws["A3"] = "📊 Key Performance Indicators"
    ws["A3"].font = Font(size=14, bold=True, color="2F5597")

    # Create KPI cards
    kpi_metrics = [
        ["📋 Total Tasks", "=COUNTA('Master Task List'!A:A)-1", "A4:B5"],
        ["✅ Completed", "=COUNTIF('Master Task List'!K:K,\"Completed\")", "C4:D5"],
        ["🔄 In Progress", "=COUNTIF('Master Task List'!K:K,\"In Progress\")", "E4:F5"],
        ["⏳ Not Started", "=COUNTIF('Master Task List'!K:K,\"Not Started\")", "G4:H5"]
    ]

    for label, formula, cell_range in kpi_metrics:
        # Label
        start_cell = cell_range.split(":")[0]
        ws[start_cell] = label
        ws[start_cell].font = Font(size=10, bold=True)
        ws[start_cell].alignment = Alignment(horizontal="center")

        # Value
        value_cell = chr(ord(start_cell[0]) + 1) + start_cell[1:]
        ws[value_cell] = formula
        ws[value_cell].font = Font(size=14, bold=True, color="2F5597")
        ws[value_cell].alignment = Alignment(horizontal="center")

        # Merge and style the KPI card
        ws.merge_cells(cell_range)
        for row in ws[cell_range]:
            for cell in row:
                cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
                cell.fill = PatternFill(start_color="F0F8FF", end_color="F0F8FF", fill_type="solid")

    # Team efficiency section
    ws["A7"] = "📈 Team Efficiency Metrics"
    ws["A7"].font = Font(size=14, bold=True, color="2F5597")

    efficiency_metrics = [
        ["Average Completion %:", "=ROUND(AVERAGE('Master Task List'!L:L),1)&\"%\""],
        ["Total Hours Estimated:", "=SUM('Master Task List'!F:F)"],
        ["Total Hours Actual:", "=SUM('Master Task List'!G:G)"],
        ["Efficiency Ratio:", "=ROUND(SUM('Master Task List'!F:F)/SUM('Master Task List'!G:G)*100,1)&\"%\""],
        ["High Priority Tasks:", "=COUNTIF('Master Task List'!E:E,\"High\")"],
        ["Overdue Tasks:", "=COUNTIFS('Master Task List'!J:J,\"<\"&TODAY(),'Master Task List'!K:K,\"<>Completed\")"]
    ]

    row = 8
    for metric in efficiency_metrics:
        ws[f"A{row}"] = metric[0]
        ws[f"B{row}"] = metric[1]
        ws[f"A{row}"].font = Font(bold=True)
        row += 1

    # Add charts section
    add_dashboard_charts(ws)

def add_navigation_buttons(wb):
    """Add navigation buttons to all sheets."""

    navigation_sheets = [
        ("🏠 Dashboard", "Dashboard"),
        ("📋 Tasks", "Master Task List"),
        ("📊 KPIs", "KPI Metrics"),
        ("👥 Team", "Task Assignment Tracker")
    ]

    for sheet_name in wb.sheetnames:
        ws = wb[sheet_name]

        # Add navigation header (avoid overlapping with data)
        nav_row = 2 if sheet_name == "Dashboard" else 1
        nav_col_start = 15  # Start from column O to avoid data overlap

        for i, (display_name, target_sheet) in enumerate(navigation_sheets):
            cell = ws.cell(row=nav_row, column=nav_col_start + i*2)
            cell.value = display_name
            cell.font = Font(size=10, bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")
            cell.border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # Add hyperlink (note: this is a simplified approach)
            if target_sheet != sheet_name:
                cell.hyperlink = f"#{target_sheet}!A1"

def apply_header_formatting(ws, num_columns):
    """Apply consistent header formatting."""

    # Header row formatting
    for col in range(1, num_columns + 1):
        cell = ws.cell(row=1, column=col)
        cell.font = Font(bold=True, color="FFFFFF")
        cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        cell.alignment = Alignment(horizontal="center")

def add_master_task_validation(ws, developers):
    """Add data validation to Master Task List sheet."""

    # Developer dropdown validation
    dev_validation = DataValidation(type="list", formula1=f'"{",".join(developers)}"')
    dev_validation.error = "Please select a valid developer"
    dev_validation.errorTitle = "Invalid Developer"
    ws.add_data_validation(dev_validation)
    dev_validation.add("D2:D1000")  # Assigned To column

    # Priority dropdown validation
    priority_validation = DataValidation(type="list", formula1='"High,Medium,Low"')
    priority_validation.error = "Please select High, Medium, or Low"
    priority_validation.errorTitle = "Invalid Priority"
    ws.add_data_validation(priority_validation)
    priority_validation.add("E2:E1000")  # Priority column

    # Status dropdown validation
    status_validation = DataValidation(type="list", formula1='"Not Started,In Progress,Completed"')
    status_validation.error = "Please select a valid status"
    status_validation.errorTitle = "Invalid Status"
    ws.add_data_validation(status_validation)
    status_validation.add("K2:K1000")  # Status column

    # Difficulty level validation (1-5)
    difficulty_validation = DataValidation(type="whole", operator="between", formula1=1, formula2=5)
    difficulty_validation.error = "Please enter a number between 1 and 5"
    difficulty_validation.errorTitle = "Invalid Difficulty Level"
    ws.add_data_validation(difficulty_validation)
    difficulty_validation.add("H2:H1000")  # Difficulty Level column

def add_master_task_conditional_formatting(ws):
    """Add conditional formatting to Master Task List sheet."""

    # Status-based formatting
    completed_fill = PatternFill(start_color="C6EFCE", end_color="C6EFCE", fill_type="solid")
    in_progress_fill = PatternFill(start_color="FFEB9C", end_color="FFEB9C", fill_type="solid")
    not_started_fill = PatternFill(start_color="FFC7CE", end_color="FFC7CE", fill_type="solid")

    # Apply status formatting to entire rows
    ws.conditional_formatting.add("A2:L1000",
        CellIsRule(operator='equal', formula=['"Completed"'],
                  stopIfTrue=True, fill=completed_fill))

    ws.conditional_formatting.add("A2:L1000",
        CellIsRule(operator='equal', formula=['"In Progress"'],
                  stopIfTrue=True, fill=in_progress_fill))

    ws.conditional_formatting.add("A2:L1000",
        CellIsRule(operator='equal', formula=['"Not Started"'],
                  stopIfTrue=True, fill=not_started_fill))

    # Priority-based formatting for Priority column
    high_priority_fill = PatternFill(start_color="FF6B6B", end_color="FF6B6B", fill_type="solid")
    medium_priority_fill = PatternFill(start_color="FFE66D", end_color="FFE66D", fill_type="solid")
    low_priority_fill = PatternFill(start_color="95E1D3", end_color="95E1D3", fill_type="solid")

    ws.conditional_formatting.add("E2:E1000",
        CellIsRule(operator='equal', formula=['"High"'], fill=high_priority_fill))
    ws.conditional_formatting.add("E2:E1000",
        CellIsRule(operator='equal', formula=['"Medium"'], fill=medium_priority_fill))
    ws.conditional_formatting.add("E2:E1000",
        CellIsRule(operator='equal', formula=['"Low"'], fill=low_priority_fill))

    # Overdue tasks formatting
    overdue_fill = PatternFill(start_color="FF0000", end_color="FF0000", fill_type="solid")
    overdue_font = Font(color="FFFFFF", bold=True)

    ws.conditional_formatting.add("J2:J1000",
        FormulaRule(formula=['AND(J2<TODAY(),K2<>"Completed")'],
                   fill=overdue_fill, font=overdue_font))

def add_dashboard_charts(ws):
    """Add charts to the main dashboard."""

    # Task Status Distribution Pie Chart
    pie_chart = PieChart()
    pie_chart.title = "Task Status Distribution"
    pie_chart.height = 10
    pie_chart.width = 15

    # Data for pie chart (we'll create a summary table first)
    ws["D15"] = "Status"
    ws["E15"] = "Count"
    ws["D16"] = "Completed"
    ws["E16"] = "=COUNTIF('Master Task List'!K:K,\"Completed\")"
    ws["D17"] = "In Progress"
    ws["E17"] = "=COUNTIF('Master Task List'!K:K,\"In Progress\")"
    ws["D18"] = "Not Started"
    ws["E18"] = "=COUNTIF('Master Task List'!K:K,\"Not Started\")"

    # Add data to pie chart
    labels = Reference(ws, min_col=4, min_row=16, max_row=18)
    data = Reference(ws, min_col=5, min_row=15, max_row=18)
    pie_chart.add_data(data, titles_from_data=True)
    pie_chart.set_categories(labels)

    # Style the pie chart
    pie_chart.dataLabels = openpyxl.chart.label.DataLabelList()
    pie_chart.dataLabels.showPercent = True

    ws.add_chart(pie_chart, "D20")

    # Developer Workload Bar Chart
    bar_chart = BarChart()
    bar_chart.title = "Tasks by Developer"
    bar_chart.height = 10
    bar_chart.width = 15

    # Data for bar chart
    ws["A15"] = "Developer"
    ws["B15"] = "Task Count"
    ws["A16"] = "Srishti Singh"
    ws["B16"] = "=COUNTIF('Master Task List'!D:D,\"Srishti Singh\")"
    ws["A17"] = "Japneet Doulani"
    ws["B17"] = "=COUNTIF('Master Task List'!D:D,\"Japneet Doulani\")"
    ws["A18"] = "Chittu Dhanraj Chavan"
    ws["B18"] = "=COUNTIF('Master Task List'!D:D,\"Chittu Dhanraj Chavan\")"
    ws["A19"] = "Shivani Singh"
    ws["B19"] = "=COUNTIF('Master Task List'!D:D,\"Shivani Singh\")"

    # Add data to bar chart
    labels = Reference(ws, min_col=1, min_row=16, max_row=19)
    data = Reference(ws, min_col=2, min_row=15, max_row=19)
    bar_chart.add_data(data, titles_from_data=True)
    bar_chart.set_categories(labels)

    ws.add_chart(bar_chart, "A20")

def create_enhanced_individual_dashboard(ws, developer_name):
    """Create enhanced individual developer dashboard with charts."""

    # Dashboard title
    ws["A1"] = f"👤 {developer_name} - Personal Dashboard"
    ws["A1"].font = Font(size=16, bold=True, color="2F5597")
    ws.merge_cells("A1:F1")
    ws["A1"].alignment = Alignment(horizontal="center")

    # Personal KPI cards
    ws["A3"] = "📊 My Performance Metrics"
    ws["A3"].font = Font(size=14, bold=True, color="2F5597")

    personal_metrics = [
        ["📋 My Tasks", f"=COUNTIF('Master Task List'!D:D,\"{developer_name}\")", "A4:B5"],
        ["✅ Completed", f"=COUNTIFS('Master Task List'!D:D,\"{developer_name}\",'Master Task List'!K:K,\"Completed\")", "C4:D5"],
        ["🔄 In Progress", f"=COUNTIFS('Master Task List'!D:D,\"{developer_name}\",'Master Task List'!K:K,\"In Progress\")", "E4:F5"]
    ]

    for label, formula, cell_range in personal_metrics:
        start_cell = cell_range.split(":")[0]
        ws[start_cell] = label
        ws[start_cell].font = Font(size=10, bold=True)
        ws[start_cell].alignment = Alignment(horizontal="center")

        # Value
        value_cell = chr(ord(start_cell[0]) + 1) + start_cell[1:]
        ws[value_cell] = formula
        ws[value_cell].font = Font(size=14, bold=True, color="2F5597")
        ws[value_cell].alignment = Alignment(horizontal="center")

        # Style the KPI card
        ws.merge_cells(cell_range)
        for row in ws[cell_range]:
            for cell in row:
                cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
                cell.fill = PatternFill(start_color="F0F8FF", end_color="F0F8FF", fill_type="solid")

    # Today's tasks section
    ws["A7"] = "📅 Today's Tasks"
    ws["A7"].font = Font(size=14, bold=True, color="2F5597")

    # Task headers
    task_headers = ["Task ID", "Task Title", "Priority", "Status", "% Complete"]
    for i, header in enumerate(task_headers):
        cell = ws.cell(row=8, column=i+1)
        cell.value = header
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color="E6F3FF", end_color="E6F3FF", fill_type="solid")

if __name__ == "__main__":
    create_ui_task_tracker()
